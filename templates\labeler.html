<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CAPTCHA Labeler Pro</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --primary-color: #6366f1;
        --primary-dark: #4f46e5;
        --primary-light: #a5b4fc;
        --secondary-color: #f1f5f9;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --border-color: #e2e8f0;
        --bg-primary: #ffffff;
        --bg-secondary: #f8fafc;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: var(--text-primary);
        line-height: 1.6;
        padding: 1rem;
      }

      .app-container {
        max-width: 1200px;
        margin: 0 auto;
        background: var(--bg-primary);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
        min-height: calc(100vh - 2rem);
        display: flex;
        flex-direction: column;
      }

      .header {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
      }

      .header-content {
        position: relative;
        z-index: 1;
      }

      .header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
      }

      .header p {
        font-size: 1.125rem;
        opacity: 0.9;
        font-weight: 400;
      }

      .main-content {
        flex: 1;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--primary-light)
        );
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .stat-card:hover::before {
        transform: scaleX(1);
      }

      .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-light);
      }

      .stat-icon {
        width: 3rem;
        height: 3rem;
        margin: 0 auto 1rem;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--primary-light)
        );
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        line-height: 1;
      }

      .stat-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .progress-section {
        margin-bottom: 2rem;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
      }

      .progress-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .progress-percentage {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--primary-color);
      }

      .progress-bar {
        width: 100%;
        height: 0.75rem;
        background: var(--secondary-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--success-color)
        );
        border-radius: var(--radius-lg);
        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
      }

      .progress-fill::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      .image-section {
        background: var(--bg-secondary);
        border-radius: var(--radius-xl);
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
        border: 1px solid var(--border-color);
      }

      .captcha-image {
        max-width: 100%;
        max-height: 300px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-lg);
        background: white;
        padding: 1rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
      }

      .captcha-image:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-lg);
      }

      .input-section {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: 2rem;
        margin-bottom: 2rem;
      }

      .input-group {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        max-width: 400px;
        margin: 0 auto;
      }

      .input-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
      }

      .label-input {
        padding: 1rem 1.25rem;
        font-size: 1.125rem;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-lg);
        text-align: center;
        text-transform: lowercase;
        font-weight: 500;
        transition: all 0.3s ease;
        background: var(--bg-primary);
        color: var(--text-primary);
      }

      .label-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        transform: translateY(-1px);
      }

      .label-input::placeholder {
        color: var(--text-muted);
      }
      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        border: none;
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        position: relative;
        overflow: hidden;
        min-height: 48px;
        touch-action: manipulation;
      }

      .btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn:hover::before {
        left: 100%;
      }

      .btn-primary {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--primary-dark)
        );
        color: white;
        box-shadow: var(--shadow-md);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-primary:active {
        transform: translateY(0);
      }

      .btn-secondary {
        background: var(--bg-primary);
        color: var(--text-secondary);
        border: 2px solid var(--border-color);
      }

      .btn-secondary:hover {
        background: var(--bg-secondary);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }

      .btn-success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        box-shadow: var(--shadow-md);
      }

      .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
        box-shadow: var(--shadow-md);
      }

      .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }

      .btn-icon {
        width: 1.25rem;
        height: 1.25rem;
      }
      .shortcuts-section {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: 1.5rem;
      }

      .shortcuts-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .shortcuts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
      }

      .shortcut-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem;
        background: var(--bg-primary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
      }

      .key {
        background: var(--text-primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
          "Courier New", monospace;
        font-size: 0.75rem;
        font-weight: 600;
        min-width: 2rem;
        text-align: center;
      }

      .shortcut-description {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }

      .loading {
        display: none;
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
      }

      .loading-spinner {
        width: 2rem;
        height: 2rem;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .alert {
        padding: 1rem 1.25rem;
        border-radius: var(--radius-lg);
        margin-bottom: 1rem;
        display: none;
        font-weight: 500;
        border-left: 4px solid;
      }

      .alert-error {
        background: #fef2f2;
        color: #991b1b;
        border-color: var(--error-color);
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border-color: var(--success-color);
      }

      .alert-icon {
        margin-right: 0.5rem;
      }

      .no-images-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-secondary);
      }

      .no-images-icon {
        font-size: 4rem;
        color: var(--success-color);
        margin-bottom: 1rem;
      }

      .no-images-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
      }

      .no-images-description {
        font-size: 1rem;
        color: var(--text-secondary);
      }

      /* Mobile Responsive Styles */
      @media (max-width: 768px) {
        body {
          padding: 0.5rem;
        }

        .app-container {
          min-height: calc(100vh - 1rem);
          border-radius: var(--radius-lg);
        }

        .header {
          padding: 1.5rem 1rem;
        }

        .header h1 {
          font-size: 2rem;
        }

        .header p {
          font-size: 1rem;
        }

        .main-content {
          padding: 1.5rem;
          gap: 1.5rem;
        }

        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 1rem;
        }

        .stat-card {
          padding: 1rem;
        }

        .stat-icon {
          width: 2.5rem;
          height: 2.5rem;
          font-size: 1rem;
        }

        .stat-number {
          font-size: 2rem;
        }

        .image-section {
          padding: 1.5rem;
        }

        .captcha-image {
          max-height: 200px;
          padding: 0.75rem;
        }

        .input-section {
          padding: 1.5rem;
        }

        .label-input {
          font-size: 16px; /* Prevent zoom on iOS */
          padding: 0.875rem 1rem;
        }

        .btn {
          padding: 0.875rem 1.5rem;
          font-size: 0.875rem;
        }

        .shortcuts-section {
          padding: 1rem;
        }

        .shortcuts-grid {
          grid-template-columns: 1fr;
          gap: 0.5rem;
        }

        .shortcut-item {
          padding: 0.75rem;
        }
      }

      @media (max-width: 480px) {
        .header h1 {
          font-size: 1.75rem;
        }

        .header p {
          font-size: 0.875rem;
        }

        .main-content {
          padding: 1rem;
          gap: 1rem;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 0.75rem;
        }

        .stat-card {
          padding: 1rem;
        }

        .stat-number {
          font-size: 1.75rem;
        }

        .image-section {
          padding: 1rem;
        }

        .captcha-image {
          max-height: 150px;
          padding: 0.5rem;
        }

        .input-section {
          padding: 1rem;
        }

        .shortcuts-section {
          padding: 1rem;
        }
      }

      /* Landscape orientation on mobile */
      @media (max-width: 768px) and (orientation: landscape) {
        .stats-grid {
          grid-template-columns: repeat(4, 1fr);
        }

        .captcha-image {
          max-height: 120px;
        }

        .header {
          padding: 1rem;
        }

        .header h1 {
          font-size: 1.5rem;
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #f1f5f9;
          --text-secondary: #cbd5e1;
          --text-muted: #64748b;
          --bg-primary: #1e293b;
          --bg-secondary: #334155;
          --border-color: #475569;
        }
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <header class="header">
        <div class="header-content">
          <h1><i class="fas fa-robot"></i> CAPTCHA Labeler Pro</h1>
          <p>Professional AI training data labeling platform</p>
        </div>
      </header>

      <main class="main-content">
        <!-- Statistics Cards -->
        <div class="stats-grid" id="stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-images"></i>
            </div>
            <div class="stat-number" id="total-images">-</div>
            <div class="stat-label">Total Images</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-number" id="labeled-count">-</div>
            <div class="stat-label">Labeled</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number" id="remaining-count">-</div>
            <div class="stat-label">Remaining</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number" id="progress-percent">-</div>
            <div class="stat-label">Progress</div>
          </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
          <div class="progress-header">
            <span class="progress-title">Labeling Progress</span>
            <span class="progress-percentage" id="progress-text">0%</span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              id="progress-fill"
              style="width: 0%"
            ></div>
          </div>
        </div>

        <!-- Alert Messages -->
        <div class="alert alert-error" id="error-message">
          <i class="fas fa-exclamation-triangle alert-icon"></i>
          <span id="error-text"></span>
        </div>
        <div class="alert alert-success" id="success-message">
          <i class="fas fa-check-circle alert-icon"></i>
          <span id="success-text"></span>
        </div>

        <!-- Loading State -->
        <div class="loading" id="loading">
          <div class="loading-spinner"></div>
          <p>Loading next image...</p>
        </div>

        <!-- Image Display Section -->
        <div class="image-section">
          <img
            id="captcha-image"
            class="captcha-image"
            src=""
            alt="CAPTCHA Image"
            style="display: none"
          />
          <div id="no-images" class="no-images-state" style="display: none">
            <div class="no-images-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <h3 class="no-images-title">All Images Labeled!</h3>
            <p class="no-images-description">
              Excellent work! You've successfully labeled all CAPTCHA images.
              Your training dataset is now ready for AI model development.
            </p>
          </div>
        </div>

        <!-- Input Section -->
        <div class="input-section">
          <div class="input-group">
            <label class="input-label" for="label-input">
              <i class="fas fa-keyboard"></i> Enter CAPTCHA Text
            </label>
            <input
              type="text"
              id="label-input"
              class="label-input"
              placeholder="Type what you see in the image..."
              maxlength="8"
              autocomplete="off"
              spellcheck="false"
            />
            <button class="btn btn-primary" onclick="submitLabel()">
              <i class="fas fa-paper-plane btn-icon"></i>
              Submit Label
            </button>
          </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="shortcuts-section">
          <h4 class="shortcuts-title">
            <i class="fas fa-keyboard"></i>
            Keyboard Shortcuts
          </h4>
          <div class="shortcuts-grid">
            <div class="shortcut-item">
              <span class="key">Enter</span>
              <span class="shortcut-description">Submit current label</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Tab</span>
              <span class="shortcut-description">Use OCR suggestion</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Esc</span>
              <span class="shortcut-description">Skip current image</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Ctrl+R</span>
              <span class="shortcut-description">Refresh statistics</span>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      let currentImage = null;

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        refreshStats();
        loadNextImage();
        setupKeyboardShortcuts();
      });

      function setupKeyboardShortcuts() {
        document.addEventListener("keydown", function (e) {
          if (e.target.tagName === "INPUT") {
            if (e.key === "Enter") {
              e.preventDefault();
              submitLabel();
            }
          }
        });
      }

      async function refreshStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          document.getElementById("total-images").textContent =
            stats.total_images;
          document.getElementById("labeled-count").textContent =
            stats.labeled_count;
          document.getElementById("remaining-count").textContent =
            stats.unlabeled_count;
          document.getElementById("progress-percent").textContent =
            stats.progress_percent.toFixed(1) + "%";
          document.getElementById("progress-fill").style.width =
            stats.progress_percent + "%";
        } catch (error) {
          showError("Failed to load statistics");
        }
      }

      async function loadNextImage() {
        showLoading(true);
        try {
          const response = await fetch("/api/next_image");
          const data = await response.json();

          if (data.image_file) {
            currentImage = data.image_file;
            document.getElementById("captcha-image").src =
              "/images/" + data.image_file;
            document.getElementById("captcha-image").style.display = "block";
            document.getElementById("no-images").style.display = "none";

            // Focus on input
            document.getElementById("label-input").focus();
            document.getElementById("label-input").value = "";
          } else {
            document.getElementById("captcha-image").style.display = "none";
            document.getElementById("no-images").style.display = "block";
          }
        } catch (error) {
          showError("Failed to load next image");
        }
        showLoading(false);
      }

      async function submitLabel() {
        const label = document.getElementById("label-input").value.trim();
        if (!label) {
          showError("Please enter a label");
          return;
        }

        try {
          const response = await fetch("/api/label", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              image_file: currentImage,
              label: label,
            }),
          });

          const result = await response.json();
          if (result.success) {
            showSuccess("Label saved successfully!");
            refreshStats();
            loadNextImage();
          } else {
            showError("Failed to save label");
          }
        } catch (error) {
          showError("Failed to submit label");
        }
      }

      function showError(message) {
        const errorEl = document.getElementById("error-message");
        errorEl.textContent = message;
        errorEl.style.display = "block";
        setTimeout(() => (errorEl.style.display = "none"), 5000);
      }

      function showSuccess(message) {
        const successEl = document.getElementById("success-message");
        successEl.textContent = message;
        successEl.style.display = "block";
        setTimeout(() => (successEl.style.display = "none"), 3000);
      }

      function showLoading(show) {
        document.getElementById("loading").style.display = show
          ? "block"
          : "none";
      }
    </script>
  </body>
</html>
