<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CAPTCHA Labeler</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: #f8fafc;
        min-height: 100vh;
        color: #334155;
        line-height: 1.6;
        padding: 20px;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: #3b82f6;
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .header p {
        font-size: 16px;
        opacity: 0.9;
      }

      .content {
        padding: 30px;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: #f1f5f9;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
      }

      .stat-number {
        font-size: 32px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 5px;
      }

      .stat-label {
        color: #64748b;
        font-size: 14px;
        font-weight: 500;
      }
      .progress-section {
        margin-bottom: 30px;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
      }

      .progress-title {
        font-size: 14px;
        font-weight: 500;
        color: #475569;
      }

      .progress-percentage {
        font-size: 14px;
        font-weight: 600;
        color: #3b82f6;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: #3b82f6;
        border-radius: 4px;
        transition: width 0.3s ease;
      }

      .image-section {
        background: #f8fafc;
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        margin-bottom: 30px;
        border: 1px solid #e2e8f0;
      }

      .captcha-image {
        max-width: 100%;
        max-height: 200px;
        border: 2px solid #cbd5e1;
        border-radius: 8px;
        background: white;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .input-section {
        text-align: center;
        margin-bottom: 30px;
      }

      .input-group {
        display: flex;
        flex-direction: column;
        gap: 15px;
        max-width: 400px;
        margin: 0 auto;
      }

      .label-input {
        padding: 15px 20px;
        font-size: 18px;
        border: 2px solid #cbd5e1;
        border-radius: 8px;
        text-align: center;
        text-transform: lowercase;
        background: white;
        color: #1e293b;
      }

      .label-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .label-input::placeholder {
        color: #94a3b8;
      }
      .btn {
        padding: 15px 30px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 48px;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }
      .shortcuts {
        background: #f1f5f9;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
      }

      .shortcuts h4 {
        font-size: 16px;
        font-weight: 600;
        color: #334155;
        margin-bottom: 15px;
      }

      .shortcut-item {
        display: inline-block;
        margin-right: 20px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #64748b;
      }

      .key {
        background: #334155;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        font-weight: 600;
      }

      .loading {
        display: none;
        text-align: center;
        padding: 40px;
        color: #64748b;
      }

      .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: none;
        font-weight: 500;
      }

      .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
      }

      .no-images {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
      }

      .no-images h3 {
        font-size: 24px;
        color: #16a34a;
        margin-bottom: 10px;
      }

      .no-images p {
        font-size: 16px;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .container {
          border-radius: 8px;
        }

        .header {
          padding: 20px;
        }

        .header h1 {
          font-size: 24px;
        }

        .header p {
          font-size: 14px;
        }

        .content {
          padding: 20px;
        }

        .stats {
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }

        .stat-card {
          padding: 15px;
        }

        .stat-number {
          font-size: 24px;
        }

        .stat-label {
          font-size: 12px;
        }

        .image-section {
          padding: 20px;
        }

        .captcha-image {
          max-height: 150px;
          padding: 10px;
        }

        .label-input {
          font-size: 16px; /* Prevent zoom on iOS */
          padding: 12px 16px;
        }

        .btn {
          padding: 12px 24px;
          font-size: 14px;
        }

        .shortcuts {
          padding: 15px;
        }

        .shortcut-item {
          display: block;
          margin-bottom: 8px;
          margin-right: 0;
        }
      }

      @media (max-width: 480px) {
        .stats {
          grid-template-columns: 1fr;
        }

        .header h1 {
          font-size: 20px;
        }

        .captcha-image {
          max-height: 120px;
        }
      }

      @media (max-width: 480px) {
        .header h1 {
          font-size: 1.75rem;
        }

        .header p {
          font-size: 0.875rem;
        }

        .main-content {
          padding: 1rem;
          gap: 1rem;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 0.75rem;
        }

        .stat-card {
          padding: 1rem;
        }

        .stat-number {
          font-size: 1.75rem;
        }

        .image-section {
          padding: 1rem;
        }

        .captcha-image {
          max-height: 150px;
          padding: 0.5rem;
        }

        .input-section {
          padding: 1rem;
        }

        .shortcuts-section {
          padding: 1rem;
        }
      }

      /* Landscape orientation on mobile */
      @media (max-width: 768px) and (orientation: landscape) {
        .stats-grid {
          grid-template-columns: repeat(4, 1fr);
        }

        .captcha-image {
          max-height: 120px;
        }

        .header {
          padding: 1rem;
        }

        .header h1 {
          font-size: 1.5rem;
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #f1f5f9;
          --text-secondary: #cbd5e1;
          --text-muted: #64748b;
          --bg-primary: #1e293b;
          --bg-secondary: #334155;
          --border-color: #475569;
        }
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <header class="header">
        <div class="header-content">
          <h1><i class="fas fa-robot"></i> CAPTCHA Labeler Pro</h1>
          <p>Professional AI training data labeling platform</p>
        </div>
      </header>

      <main class="main-content">
        <!-- Statistics Cards -->
        <div class="stats-grid" id="stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-images"></i>
            </div>
            <div class="stat-number" id="total-images">-</div>
            <div class="stat-label">Total Images</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-number" id="labeled-count">-</div>
            <div class="stat-label">Labeled</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number" id="remaining-count">-</div>
            <div class="stat-label">Remaining</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number" id="progress-percent">-</div>
            <div class="stat-label">Progress</div>
          </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
          <div class="progress-header">
            <span class="progress-title">Labeling Progress</span>
            <span class="progress-percentage" id="progress-text">0%</span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              id="progress-fill"
              style="width: 0%"
            ></div>
          </div>
        </div>

        <!-- Alert Messages -->
        <div class="alert alert-error" id="error-message">
          <i class="fas fa-exclamation-triangle alert-icon"></i>
          <span id="error-text"></span>
        </div>
        <div class="alert alert-success" id="success-message">
          <i class="fas fa-check-circle alert-icon"></i>
          <span id="success-text"></span>
        </div>

        <!-- Loading State -->
        <div class="loading" id="loading">
          <div class="loading-spinner"></div>
          <p>Loading next image...</p>
        </div>

        <!-- Image Display Section -->
        <div class="image-section">
          <img
            id="captcha-image"
            class="captcha-image"
            src=""
            alt="CAPTCHA Image"
            style="display: none"
          />
          <div id="no-images" class="no-images-state" style="display: none">
            <div class="no-images-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <h3 class="no-images-title">All Images Labeled!</h3>
            <p class="no-images-description">
              Excellent work! You've successfully labeled all CAPTCHA images.
              Your training dataset is now ready for AI model development.
            </p>
          </div>
        </div>

        <!-- Input Section -->
        <div class="input-section">
          <div class="input-group">
            <label class="input-label" for="label-input">
              <i class="fas fa-keyboard"></i> Enter CAPTCHA Text
            </label>
            <input
              type="text"
              id="label-input"
              class="label-input"
              placeholder="Type what you see in the image..."
              maxlength="8"
              autocomplete="off"
              spellcheck="false"
            />
            <button class="btn btn-primary" onclick="submitLabel()">
              <i class="fas fa-paper-plane btn-icon"></i>
              Submit Label
            </button>
          </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="shortcuts-section">
          <h4 class="shortcuts-title">
            <i class="fas fa-keyboard"></i>
            Keyboard Shortcuts
          </h4>
          <div class="shortcuts-grid">
            <div class="shortcut-item">
              <span class="key">Enter</span>
              <span class="shortcut-description">Submit current label</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Tab</span>
              <span class="shortcut-description">Use OCR suggestion</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Esc</span>
              <span class="shortcut-description">Skip current image</span>
            </div>
            <div class="shortcut-item">
              <span class="key">Ctrl+R</span>
              <span class="shortcut-description">Refresh statistics</span>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      let currentImage = null;

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        refreshStats();
        loadNextImage();
        setupKeyboardShortcuts();
      });

      function setupKeyboardShortcuts() {
        document.addEventListener("keydown", function (e) {
          if (e.target.tagName === "INPUT") {
            if (e.key === "Enter") {
              e.preventDefault();
              submitLabel();
            }
          }
        });
      }

      async function refreshStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          document.getElementById("total-images").textContent =
            stats.total_images;
          document.getElementById("labeled-count").textContent =
            stats.labeled_count;
          document.getElementById("remaining-count").textContent =
            stats.unlabeled_count;
          document.getElementById("progress-percent").textContent =
            stats.progress_percent.toFixed(1) + "%";
          document.getElementById("progress-text").textContent =
            stats.progress_percent.toFixed(1) + "%";
          document.getElementById("progress-fill").style.width =
            stats.progress_percent + "%";
        } catch (error) {
          showError("Failed to load statistics");
        }
      }

      async function loadNextImage() {
        showLoading(true);
        try {
          const response = await fetch("/api/next_image");
          const data = await response.json();

          if (data.image_file) {
            currentImage = data.image_file;
            document.getElementById("captcha-image").src =
              "/images/" + data.image_file;
            document.getElementById("captcha-image").style.display = "block";
            document.getElementById("no-images").style.display = "none";

            // Focus on input
            document.getElementById("label-input").focus();
            document.getElementById("label-input").value = "";
          } else {
            document.getElementById("captcha-image").style.display = "none";
            document.getElementById("no-images").style.display = "block";
          }
        } catch (error) {
          showError("Failed to load next image");
        }
        showLoading(false);
      }

      async function submitLabel() {
        const label = document.getElementById("label-input").value.trim();
        if (!label) {
          showError("Please enter a label");
          return;
        }

        try {
          const response = await fetch("/api/label", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              image_file: currentImage,
              label: label,
            }),
          });

          const result = await response.json();
          if (result.success) {
            showSuccess("Label saved successfully!");
            refreshStats();
            loadNextImage();
          } else {
            showError("Failed to save label");
          }
        } catch (error) {
          showError("Failed to submit label");
        }
      }

      function showError(message) {
        const errorEl = document.getElementById("error-message");
        errorEl.textContent = message;
        errorEl.style.display = "block";
        setTimeout(() => (errorEl.style.display = "none"), 5000);
      }

      function showSuccess(message) {
        const successEl = document.getElementById("success-message");
        successEl.textContent = message;
        successEl.style.display = "block";
        setTimeout(() => (successEl.style.display = "none"), 3000);
      }

      function showLoading(show) {
        document.getElementById("loading").style.display = show
          ? "block"
          : "none";
      }
    </script>
  </body>
</html>
