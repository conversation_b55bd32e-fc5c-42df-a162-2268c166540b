<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CAPTCHA Labeler</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 10px;
        background-color: #f5f5f5;
        min-height: 100vh;
        box-sizing: border-box;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
      }
      .header {
        text-align: center;
        margin-bottom: 20px;
      }
      .header h1 {
        font-size: 1.8em;
        margin: 0 0 10px 0;
      }
      .header p {
        margin: 0;
        font-size: 0.9em;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
        background: #e8f4f8;
        border-radius: 8px;
      }
      .stat-item {
        text-align: center;
      }
      .stat-number {
        font-size: 1.5em;
        font-weight: bold;
        color: #2c3e50;
      }
      .stat-label {
        color: #7f8c8d;
        margin-top: 5px;
        font-size: 0.85em;
      }
      .image-section {
        text-align: center;
        margin-bottom: 20px;
      }
      .captcha-image {
        max-width: 100%;
        max-height: 200px;
        border: 3px solid #3498db;
        border-radius: 8px;
        margin-bottom: 20px;
        background: white;
        padding: 10px;
        box-sizing: border-box;
        height: auto;
      }
      .input-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }
      .label-input {
        padding: 12px 20px;
        font-size: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        width: 100%;
        max-width: 300px;
        text-align: center;
        text-transform: lowercase;
        box-sizing: border-box;
      }
      .label-input:focus {
        outline: none;
        border-color: #3498db;
      }
      .btn {
        padding: 12px 24px;
        font-size: 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.3s;
        min-width: 120px;
        box-sizing: border-box;
      }
      .btn-primary {
        background-color: #3498db;
        color: white;
      }
      .btn-primary:hover {
        background-color: #2980b9;
      }
      .btn-secondary {
        background-color: #95a5a6;
        color: white;
      }
      .btn-secondary:hover {
        background-color: #7f8c8d;
      }
      .btn-success {
        background-color: #27ae60;
        color: white;
      }
      .btn-success:hover {
        background-color: #229954;
      }
      .btn-warning {
        background-color: #f39c12;
        color: white;
      }
      .btn-warning:hover {
        background-color: #e67e22;
      }
      .controls {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        margin-bottom: 20px;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background: #ecf0f1;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
      }
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3498db, #2ecc71);
        transition: width 0.3s ease;
      }
      .shortcuts {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
      }
      .shortcuts h4 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 1em;
      }
      .shortcut-item {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 8px;
        font-size: 0.9em;
      }
      .key {
        background: #34495e;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-family: monospace;
        font-size: 0.85em;
      }
      .loading {
        display: none;
        text-align: center;
        color: #7f8c8d;
        padding: 20px;
      }
      .error {
        background: #ffebee;
        color: #c62828;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 15px;
        display: none;
        font-size: 0.9em;
      }
      .success {
        background: #e8f5e8;
        color: #2e7d32;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 15px;
        display: none;
        font-size: 0.9em;
      }

      /* Mobile-specific styles */
      @media (max-width: 768px) {
        body {
          padding: 5px;
        }

        .container {
          padding: 15px;
          border-radius: 8px;
        }

        .header h1 {
          font-size: 1.5em;
        }

        .stats {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
          padding: 12px;
        }

        .stat-number {
          font-size: 1.3em;
        }

        .stat-label {
          font-size: 0.8em;
        }

        .captcha-image {
          max-height: 150px;
          padding: 8px;
        }

        .input-section {
          gap: 12px;
        }

        .label-input {
          font-size: 16px;
          padding: 14px 16px;
          /* Prevent zoom on iOS */
          -webkit-appearance: none;
          appearance: none;
          border-radius: 6px;
        }

        .btn {
          padding: 14px 20px;
          font-size: 16px;
          width: 100%;
          max-width: 300px;
          /* Ensure touch-friendly size */
          min-height: 48px;
          touch-action: manipulation;
        }

        .shortcuts {
          padding: 12px;
        }

        .shortcut-item {
          display: block;
          margin-bottom: 8px;
          margin-right: 0;
        }
      }

      @media (max-width: 480px) {
        .header h1 {
          font-size: 1.3em;
        }

        .header p {
          font-size: 0.85em;
        }

        .stats {
          grid-template-columns: 1fr;
          gap: 8px;
        }

        .stat-number {
          font-size: 1.2em;
        }

        .captcha-image {
          max-height: 120px;
          padding: 6px;
          border-width: 2px;
        }

        .label-input {
          font-size: 16px;
          padding: 12px 14px;
        }

        .btn {
          padding: 12px 16px;
          min-height: 44px;
        }

        .shortcuts {
          padding: 10px;
        }

        .shortcuts h4 {
          font-size: 0.9em;
        }
      }

      /* Landscape orientation on mobile */
      @media (max-width: 768px) and (orientation: landscape) {
        .stats {
          grid-template-columns: repeat(4, 1fr);
          gap: 8px;
        }

        .captcha-image {
          max-height: 100px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔤 CAPTCHA Labeler</h1>
        <p>Efficiently label your CAPTCHA images for training</p>
      </div>

      <div class="stats" id="stats">
        <div class="stat-item">
          <div class="stat-number" id="total-images">-</div>
          <div class="stat-label">Total Images</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="labeled-count">-</div>
          <div class="stat-label">Labeled</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="remaining-count">-</div>
          <div class="stat-label">Remaining</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="progress-percent">-</div>
          <div class="stat-label">Progress %</div>
        </div>
      </div>

      <div class="progress-bar">
        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
      </div>

      <div class="error" id="error-message"></div>
      <div class="success" id="success-message"></div>
      <div class="loading" id="loading">Loading...</div>

      <div class="image-section">
        <img
          id="captcha-image"
          class="captcha-image"
          src=""
          alt="CAPTCHA Image"
          style="display: none"
        />
        <div id="no-images" style="display: none">
          <h3>🎉 All images have been labeled!</h3>
          <p>Great job! You can now organize your data for training.</p>
        </div>
      </div>

      <div class="input-section">
        <input
          type="text"
          id="label-input"
          class="label-input"
          placeholder="Enter CAPTCHA text"
          maxlength="8"
        />
        <button class="btn btn-primary" onclick="submitLabel()">Submit</button>
      </div>

      <div class="shortcuts">
        <h4>⌨️ Keyboard Shortcuts</h4>
        <div class="shortcut-item">
          <span class="key">Enter</span> Submit label
        </div>
      </div>
    </div>

    <script>
      let currentImage = null;

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        refreshStats();
        loadNextImage();
        setupKeyboardShortcuts();
      });

      function setupKeyboardShortcuts() {
        document.addEventListener("keydown", function (e) {
          if (e.target.tagName === "INPUT") {
            if (e.key === "Enter") {
              e.preventDefault();
              submitLabel();
            }
          }
        });
      }

      async function refreshStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          document.getElementById("total-images").textContent =
            stats.total_images;
          document.getElementById("labeled-count").textContent =
            stats.labeled_count;
          document.getElementById("remaining-count").textContent =
            stats.unlabeled_count;
          document.getElementById("progress-percent").textContent =
            stats.progress_percent.toFixed(1) + "%";
          document.getElementById("progress-fill").style.width =
            stats.progress_percent + "%";
        } catch (error) {
          showError("Failed to load statistics");
        }
      }

      async function loadNextImage() {
        showLoading(true);
        try {
          const response = await fetch("/api/next_image");
          const data = await response.json();

          if (data.image_file) {
            currentImage = data.image_file;
            document.getElementById("captcha-image").src =
              "/images/" + data.image_file;
            document.getElementById("captcha-image").style.display = "block";
            document.getElementById("no-images").style.display = "none";

            // Focus on input
            document.getElementById("label-input").focus();
            document.getElementById("label-input").value = "";
          } else {
            document.getElementById("captcha-image").style.display = "none";
            document.getElementById("no-images").style.display = "block";
          }
        } catch (error) {
          showError("Failed to load next image");
        }
        showLoading(false);
      }

      async function submitLabel() {
        const label = document.getElementById("label-input").value.trim();
        if (!label) {
          showError("Please enter a label");
          return;
        }

        try {
          const response = await fetch("/api/label", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              image_file: currentImage,
              label: label,
            }),
          });

          const result = await response.json();
          if (result.success) {
            showSuccess("Label saved successfully!");
            refreshStats();
            loadNextImage();
          } else {
            showError("Failed to save label");
          }
        } catch (error) {
          showError("Failed to submit label");
        }
      }

      function showError(message) {
        const errorEl = document.getElementById("error-message");
        errorEl.textContent = message;
        errorEl.style.display = "block";
        setTimeout(() => (errorEl.style.display = "none"), 5000);
      }

      function showSuccess(message) {
        const successEl = document.getElementById("success-message");
        successEl.textContent = message;
        successEl.style.display = "block";
        setTimeout(() => (successEl.style.display = "none"), 3000);
      }

      function showLoading(show) {
        document.getElementById("loading").style.display = show
          ? "block"
          : "none";
      }
    </script>
  </body>
</html>
